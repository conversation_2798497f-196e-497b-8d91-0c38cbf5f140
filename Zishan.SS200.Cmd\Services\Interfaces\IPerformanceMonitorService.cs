using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// 性能监控服务接口
    /// </summary>
    public interface IPerformanceMonitorService
    {
        #region 内存监控
        
        /// <summary>
        /// 获取当前内存使用情况
        /// </summary>
        /// <returns>内存使用信息</returns>
        MemoryUsageInfo GetCurrentMemoryUsage();
        
        /// <summary>
        /// 检查是否需要执行GC
        /// </summary>
        /// <param name="currentIteration">当前迭代次数</param>
        /// <returns>是否需要GC</returns>
        bool ShouldExecuteGC(int currentIteration);
        
        /// <summary>
        /// 执行智能GC回收
        /// </summary>
        /// <returns>GC执行结果</returns>
        Task<GCResult> ExecuteSmartGCAsync();
        
        #endregion
        
        #region 性能计时
        
        /// <summary>
        /// 开始性能计时
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>计时器ID</returns>
        string StartPerformanceTimer(string operationName);
        
        /// <summary>
        /// 停止性能计时
        /// </summary>
        /// <param name="timerId">计时器ID</param>
        /// <returns>执行时间</returns>
        TimeSpan StopPerformanceTimer(string timerId);
        
        /// <summary>
        /// 测量异步操作性能
        /// </summary>
        /// <param name="operation">异步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="warningThresholdMs">警告阈值（毫秒）</param>
        /// <returns>操作结果和执行时间</returns>
        Task<PerformanceResult<T>> MeasureAsync<T>(Func<Task<T>> operation, string operationName, int warningThresholdMs = 2000);
        
        /// <summary>
        /// 测量同步操作性能
        /// </summary>
        /// <param name="operation">同步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="warningThresholdMs">警告阈值（毫秒）</param>
        /// <returns>操作结果和执行时间</returns>
        PerformanceResult<T> Measure<T>(Func<T> operation, string operationName, int warningThresholdMs = 2000);
        
        #endregion
        
        #region 性能统计
        
        /// <summary>
        /// 获取性能统计信息
        /// </summary>
        /// <returns>性能统计</returns>
        PerformanceStatistics GetPerformanceStatistics();
        
        /// <summary>
        /// 重置性能统计
        /// </summary>
        void ResetStatistics();
        
        /// <summary>
        /// 导出性能报告
        /// </summary>
        /// <returns>性能报告</returns>
        Task<string> ExportPerformanceReportAsync();
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 性能警告事件
        /// </summary>
        event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;
        
        /// <summary>
        /// 内存使用过高事件
        /// </summary>
        event EventHandler<MemoryUsageEventArgs> HighMemoryUsage;
        
        #endregion
    }
    
    /// <summary>
    /// 内存使用信息
    /// </summary>
    public class MemoryUsageInfo
    {
        public long CurrentMemoryMB { get; set; }
        public long InitialMemoryMB { get; set; }
        public long MemoryIncreaseMB { get; set; }
        public double MemoryIncreasePercentage { get; set; }
        public DateTime MeasureTime { get; set; }
    }
    
    /// <summary>
    /// GC执行结果
    /// </summary>
    public class GCResult
    {
        public bool Success { get; set; }
        public long MemoryBeforeGC { get; set; }
        public long MemoryAfterGC { get; set; }
        public long FreedMemoryMB { get; set; }
        public TimeSpan Duration { get; set; }
        public int GCExecutionCount { get; set; }
    }
    
    /// <summary>
    /// 性能测量结果
    /// </summary>
    public class PerformanceResult<T>
    {
        public T Result { get; set; }
        public TimeSpan Duration { get; set; }
        public bool IsWarning { get; set; }
        public string OperationName { get; set; }
        public Exception Exception { get; set; }
    }
    
    /// <summary>
    /// 性能统计信息
    /// </summary>
    public class PerformanceStatistics
    {
        public int TotalOperations { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public int WarningCount { get; set; }
        public Dictionary<string, OperationStatistics> OperationStats { get; set; }
    }
    
    /// <summary>
    /// 操作统计信息
    /// </summary>
    public class OperationStatistics
    {
        public string OperationName { get; set; }
        public int ExecutionCount { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public TimeSpan AverageDuration { get; set; }
        public TimeSpan MaxDuration { get; set; }
        public TimeSpan MinDuration { get; set; }
        public int WarningCount { get; set; }
    }
    
    /// <summary>
    /// 性能警告事件参数
    /// </summary>
    public class PerformanceWarningEventArgs : EventArgs
    {
        public string OperationName { get; set; }
        public TimeSpan Duration { get; set; }
        public int ThresholdMs { get; set; }
        public string Message { get; set; }
    }
    
    /// <summary>
    /// 内存使用事件参数
    /// </summary>
    public class MemoryUsageEventArgs : EventArgs
    {
        public MemoryUsageInfo MemoryInfo { get; set; }
        public int ThresholdMB { get; set; }
        public string Message { get; set; }
    }
}
