using System;

namespace Zishan.SS200.Cmd.Constants
{
    /// <summary>
    /// 晶圆搬运相关常量定义
    /// </summary>
    public static class TransferWaferConstants
    {
        #region 延迟配置常量
        
        /// <summary>
        /// UI更新延迟（毫秒）
        /// </summary>
        public const int UI_UPDATE_DELAY = 50;
        
        /// <summary>
        /// UI显示延迟（毫秒）
        /// </summary>
        public const int UI_DISPLAY_DELAY = 100;
        
        /// <summary>
        /// PLC自动点击延迟（毫秒）
        /// </summary>
        public const int PLC_AUTO_CLICK_DELAY = 200;
        
        /// <summary>
        /// 工艺等待延迟（毫秒）
        /// </summary>
        public const int PROCESS_WAIT_DELAY = 50;
        
        /// <summary>
        /// PinSearch延迟（毫秒）
        /// </summary>
        public const int PIN_SEARCH_DELAY = 50;
        
        #endregion
        
        #region 性能监控常量
        
        /// <summary>
        /// GC执行间隔（循环次数）
        /// </summary>
        public const int GC_EXECUTION_INTERVAL = 3;
        
        /// <summary>
        /// 内存阈值（MB）
        /// </summary>
        public const int MEMORY_THRESHOLD_MB = 100;
        
        /// <summary>
        /// 性能警告阈值（毫秒）
        /// </summary>
        public const int PERFORMANCE_WARNING_THRESHOLD_MS = 2000;
        
        /// <summary>
        /// 搬运操作警告阈值（毫秒）
        /// </summary>
        public const int TRANSFER_WARNING_THRESHOLD_MS = 10000;
        
        /// <summary>
        /// PinSearch操作警告阈值（毫秒）
        /// </summary>
        public const int PIN_SEARCH_WARNING_THRESHOLD_MS = 5000;
        
        #endregion
        
        #region 默认值常量
        
        /// <summary>
        /// 默认Wafer总数
        /// </summary>
        public const int DEFAULT_WAFER_TOTAL_COUNT = 25;
        
        /// <summary>
        /// 默认移动速度（秒）
        /// </summary>
        public const int DEFAULT_MOVE_SPEED = 3;
        
        /// <summary>
        /// 默认循环次数
        /// </summary>
        public const int DEFAULT_LOOP_COUNT = 1;
        
        /// <summary>
        /// 无限循环标识
        /// </summary>
        public const int INFINITE_LOOP = -1;
        
        /// <summary>
        /// 机械臂默认槽位
        /// </summary>
        public const int ROBOT_ARM_DEFAULT_SLOT = 1;
        
        #endregion
        
        #region 容量限制常量
        
        /// <summary>
        /// 设置参数总数量
        /// </summary>
        public const int TOTAL_PARAMETERS_COUNT = 774;
        
        /// <summary>
        /// 最大编辑行数限制
        /// </summary>
        public const int MAX_EDIT_LINES = 500;
        
        #endregion
        
        #region 超时配置常量
        
        /// <summary>
        /// 调试模式命令超时时间（秒）
        /// </summary>
        public const int DEBUG_COMMAND_TIMEOUT = 120;
        
        /// <summary>
        /// 正常模式命令超时时间（秒）
        /// </summary>
        public const int NORMAL_COMMAND_TIMEOUT = 60;
        
        #endregion
        
        #region 消息常量
        
        /// <summary>
        /// 成功消息
        /// </summary>
        public static class Messages
        {
            public const string TRANSFER_SUCCESS = "晶圆搬运成功";
            public const string TRANSFER_FAILED = "晶圆搬运失败";
            public const string PIN_SEARCH_SUCCESS = "PinSearch执行成功";
            public const string PIN_SEARCH_FAILED = "PinSearch执行失败";
            public const string RESET_SUCCESS = "重置操作完成";
            public const string LOOP_STARTED = "开始循环执行工艺流程";
            public const string LOOP_STOPPED = "循环执行已停止";
            public const string DEVICE_NOT_CONNECTED = "Robot设备未连接";
            public const string RECIPE_NOT_SELECTED = "请先选择配方";
            public const string SLOT_CANNOT_BE_EMPTY = "slot不能为空";
            public const string SWITCH_TO_AUTO_MODE = "请先切换到自动模式";
            public const string STOP_RUNNING_FIRST = "请先停止运行";
        }
        
        #endregion
        
        #region 日志分类常量
        
        /// <summary>
        /// 日志分类
        /// </summary>
        public static class LogCategories
        {
            public const string TRANSFER_OPERATION = "搬运操作";
            public const string PIN_SEARCH_OPERATION = "PinSearch操作";
            public const string PERFORMANCE_MONITORING = "性能监控";
            public const string SYSTEM_STATUS = "系统状态";
            public const string ERROR_HANDLING = "错误处理";
            public const string RECIPE_MANAGEMENT = "配方管理";
        }
        
        #endregion
    }
}
