using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Services.Interfaces;
using Wu.Wpf.Common;

namespace Zishan.SS200.Cmd.Services.Implementations
{
    /// <summary>
    /// 性能监控服务实现
    /// </summary>
    public class PerformanceMonitorService : IPerformanceMonitorService
    {
        #region 私有字段

        private readonly ConcurrentDictionary<string, Stopwatch> _activeTimers;
        private readonly ConcurrentDictionary<string, OperationStatistics> _operationStats;
        private readonly object _lockObject = new object();
        private long _initialMemory;
        private int _gcExecutionCount;
        private DateTime _startTime;

        #endregion

        #region 构造函数

        public PerformanceMonitorService()
        {
            _activeTimers = new ConcurrentDictionary<string, Stopwatch>();
            _operationStats = new ConcurrentDictionary<string, OperationStatistics>();
            _initialMemory = GC.GetTotalMemory(false);
            _startTime = DateTime.Now;
        }

        #endregion

        #region 事件

        public event EventHandler<PerformanceWarningEventArgs> PerformanceWarning;
        public event EventHandler<MemoryUsageEventArgs> HighMemoryUsage;

        #endregion

        #region 内存监控实现

        public MemoryUsageInfo GetCurrentMemoryUsage()
        {
            var currentMemory = GC.GetTotalMemory(false);
            var memoryIncrease = currentMemory - _initialMemory;

            return new MemoryUsageInfo
            {
                CurrentMemoryMB = currentMemory / 1024 / 1024,
                InitialMemoryMB = _initialMemory / 1024 / 1024,
                MemoryIncreaseMB = memoryIncrease / 1024 / 1024,
                MemoryIncreasePercentage = _initialMemory > 0 ? (double)memoryIncrease / _initialMemory * 100 : 0,
                MeasureTime = DateTime.Now
            };
        }

        public bool ShouldExecuteGC(int currentIteration)
        {
            if (currentIteration <= 0) return false;

            var memoryInfo = GetCurrentMemoryUsage();

            // 基于迭代次数和内存使用情况决定是否执行GC
            bool shouldExecuteByIteration = currentIteration % TransferWaferConstants.GC_EXECUTION_INTERVAL == 0;
            bool shouldExecuteByMemory = memoryInfo.CurrentMemoryMB > TransferWaferConstants.MEMORY_THRESHOLD_MB;

            return shouldExecuteByIteration || shouldExecuteByMemory;
        }

        public async Task<GCResult> ExecuteSmartGCAsync()
        {
            return await Task.Run(() =>
            {
                var stopwatch = Stopwatch.StartNew();
                var memoryBefore = GC.GetTotalMemory(false);

                try
                {
                    // 执行分代GC
                    GC.Collect(0, GCCollectionMode.Optimized);
                    GC.Collect(1, GCCollectionMode.Optimized);
                    GC.Collect(2, GCCollectionMode.Optimized);
                    GC.WaitForPendingFinalizers();
                    GC.Collect();

                    var memoryAfter = GC.GetTotalMemory(true);
                    stopwatch.Stop();

                    _gcExecutionCount++;

                    var result = new GCResult
                    {
                        Success = true,
                        MemoryBeforeGC = memoryBefore,
                        MemoryAfterGC = memoryAfter,
                        FreedMemoryMB = (memoryBefore - memoryAfter) / 1024 / 1024,
                        Duration = stopwatch.Elapsed,
                        GCExecutionCount = _gcExecutionCount
                    };

                    // UILogService.AddLog($"[智能GC] 释放内存: {result.FreedMemoryMB:F2} MB, 耗时: {result.Duration.TotalMilliseconds:F0} ms (第{_gcExecutionCount}次)");

                    return result;
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    // UILogService.AddErrorLog($"智能GC执行失败: {ex.Message}");

                    return new GCResult
                    {
                        Success = false,
                        MemoryBeforeGC = memoryBefore,
                        MemoryAfterGC = memoryBefore,
                        Duration = stopwatch.Elapsed
                    };
                }
            });
        }

        #endregion

        #region 性能计时实现

        public string StartPerformanceTimer(string operationName)
        {
            var timerId = $"{operationName}_{Guid.NewGuid():N}";
            var stopwatch = Stopwatch.StartNew();

            _activeTimers.TryAdd(timerId, stopwatch);
            return timerId;
        }

        public TimeSpan StopPerformanceTimer(string timerId)
        {
            if (_activeTimers.TryRemove(timerId, out var stopwatch))
            {
                stopwatch.Stop();
                return stopwatch.Elapsed;
            }

            return TimeSpan.Zero;
        }

        public async Task<PerformanceResult<T>> MeasureAsync<T>(Func<Task<T>> operation, string operationName, int warningThresholdMs = 2000)
        {
            var stopwatch = Stopwatch.StartNew();
            Exception exception = null;
            T result = default(T);

            try
            {
                result = await operation();
            }
            catch (Exception ex)
            {
                exception = ex;
            }
            finally
            {
                stopwatch.Stop();
            }

            var performanceResult = new PerformanceResult<T>
            {
                Result = result,
                Duration = stopwatch.Elapsed,
                IsWarning = stopwatch.ElapsedMilliseconds > warningThresholdMs,
                OperationName = operationName,
                Exception = exception
            };

            // 记录统计信息
            RecordOperationStatistics(operationName, stopwatch.Elapsed, performanceResult.IsWarning);

            // 触发警告事件
            if (performanceResult.IsWarning)
            {
                OnPerformanceWarning(operationName, stopwatch.Elapsed, warningThresholdMs);
            }

            return performanceResult;
        }

        public PerformanceResult<T> Measure<T>(Func<T> operation, string operationName, int warningThresholdMs = 2000)
        {
            var stopwatch = Stopwatch.StartNew();
            Exception exception = null;
            T result = default(T);

            try
            {
                result = operation();
            }
            catch (Exception ex)
            {
                exception = ex;
            }
            finally
            {
                stopwatch.Stop();
            }

            var performanceResult = new PerformanceResult<T>
            {
                Result = result,
                Duration = stopwatch.Elapsed,
                IsWarning = stopwatch.ElapsedMilliseconds > warningThresholdMs,
                OperationName = operationName,
                Exception = exception
            };

            // 记录统计信息
            RecordOperationStatistics(operationName, stopwatch.Elapsed, performanceResult.IsWarning);

            // 触发警告事件
            if (performanceResult.IsWarning)
            {
                OnPerformanceWarning(operationName, stopwatch.Elapsed, warningThresholdMs);
            }

            return performanceResult;
        }

        #endregion

        #region 性能统计实现

        public PerformanceStatistics GetPerformanceStatistics()
        {
            lock (_lockObject)
            {
                var allStats = _operationStats.Values.ToList();

                if (!allStats.Any())
                {
                    return new PerformanceStatistics
                    {
                        OperationStats = new Dictionary<string, OperationStatistics>()
                    };
                }

                var totalOperations = allStats.Sum(s => s.ExecutionCount);
                var totalDuration = TimeSpan.FromTicks(allStats.Sum(s => s.TotalDuration.Ticks));
                var averageDuration = totalOperations > 0 ? TimeSpan.FromTicks(totalDuration.Ticks / totalOperations) : TimeSpan.Zero;
                var maxDuration = allStats.Max(s => s.MaxDuration);
                var minDuration = allStats.Min(s => s.MinDuration);
                var warningCount = allStats.Sum(s => s.WarningCount);

                return new PerformanceStatistics
                {
                    TotalOperations = totalOperations,
                    TotalDuration = totalDuration,
                    AverageDuration = averageDuration,
                    MaxDuration = maxDuration,
                    MinDuration = minDuration,
                    WarningCount = warningCount,
                    OperationStats = _operationStats.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
                };
            }
        }

        public void ResetStatistics()
        {
            lock (_lockObject)
            {
                _operationStats.Clear();
                _gcExecutionCount = 0;
                _initialMemory = GC.GetTotalMemory(false);
                _startTime = DateTime.Now;
            }
        }

        public async Task<string> ExportPerformanceReportAsync()
        {
            return await Task.Run(() =>
            {
                var stats = GetPerformanceStatistics();
                var memoryInfo = GetCurrentMemoryUsage();

                var report = $@"
=== 性能监控报告 ===
生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
监控开始时间: {_startTime:yyyy-MM-dd HH:mm:ss}
监控持续时间: {DateTime.Now - _startTime}

=== 内存使用情况 ===
当前内存: {memoryInfo.CurrentMemoryMB} MB
初始内存: {memoryInfo.InitialMemoryMB} MB
内存增长: {memoryInfo.MemoryIncreaseMB} MB ({memoryInfo.MemoryIncreasePercentage:F2}%)
GC执行次数: {_gcExecutionCount}

=== 操作统计 ===
总操作数: {stats.TotalOperations}
总耗时: {stats.TotalDuration}
平均耗时: {stats.AverageDuration}
最大耗时: {stats.MaxDuration}
最小耗时: {stats.MinDuration}
警告次数: {stats.WarningCount}

=== 详细操作统计 ===";

                foreach (var kvp in stats.OperationStats)
                {
                    var opStat = kvp.Value;
                    report += $@"
{opStat.OperationName}:
  执行次数: {opStat.ExecutionCount}
  总耗时: {opStat.TotalDuration}
  平均耗时: {opStat.AverageDuration}
  最大耗时: {opStat.MaxDuration}
  最小耗时: {opStat.MinDuration}
  警告次数: {opStat.WarningCount}";
                }

                return report;
            });
        }

        #endregion

        #region 私有方法

        private void RecordOperationStatistics(string operationName, TimeSpan duration, bool isWarning)
        {
            _operationStats.AddOrUpdate(operationName,
                new OperationStatistics
                {
                    OperationName = operationName,
                    ExecutionCount = 1,
                    TotalDuration = duration,
                    AverageDuration = duration,
                    MaxDuration = duration,
                    MinDuration = duration,
                    WarningCount = isWarning ? 1 : 0
                },
                (key, existing) =>
                {
                    existing.ExecutionCount++;
                    existing.TotalDuration = existing.TotalDuration.Add(duration);
                    existing.AverageDuration = TimeSpan.FromTicks(existing.TotalDuration.Ticks / existing.ExecutionCount);
                    existing.MaxDuration = duration > existing.MaxDuration ? duration : existing.MaxDuration;
                    existing.MinDuration = duration < existing.MinDuration ? duration : existing.MinDuration;
                    if (isWarning) existing.WarningCount++;
                    return existing;
                });
        }

        private void OnPerformanceWarning(string operationName, TimeSpan duration, int thresholdMs)
        {
            var message = $"操作 '{operationName}' 执行时间 {duration.TotalMilliseconds:F0}ms 超过阈值 {thresholdMs}ms";
            // UILogService.AddWarningLog(message);

            PerformanceWarning?.Invoke(this, new PerformanceWarningEventArgs
            {
                OperationName = operationName,
                Duration = duration,
                ThresholdMs = thresholdMs,
                Message = message
            });
        }

        #endregion
    }
}
