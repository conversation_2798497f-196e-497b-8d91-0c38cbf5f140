using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Mvvm;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 重构后的晶圆搬运视图模型 - 职责分离版本
    /// 主要职责：UI数据绑定和用户交互
    /// </summary>
    public partial class TransferWaferViewModelRefactored : ViewModel, IDisposable
    {
        #region 依赖服务
        
        private readonly IRobotControlService _robotControlService;
        private readonly IPinSearchService _pinSearchService;
        private readonly IPerformanceMonitorService _performanceMonitorService;
        private readonly IProcessLoopService _processLoopService;
        private readonly IAlarmManagementService _alarmManagementService;
        
        #endregion
        
        #region 取消令牌
        
        private CancellationTokenSource _mainCancellationTokenSource;
        private CancellationTokenSource _loopCancellationTokenSource;
        
        #endregion
        
        #region 构造函数
        
        public TransferWaferViewModelRefactored(
            IRobotControlService robotControlService,
            IPinSearchService pinSearchService,
            IPerformanceMonitorService performanceMonitorService,
            IProcessLoopService processLoopService,
            IAlarmManagementService alarmManagementService)
        {
            _robotControlService = robotControlService ?? throw new ArgumentNullException(nameof(robotControlService));
            _pinSearchService = pinSearchService ?? throw new ArgumentNullException(nameof(pinSearchService));
            _performanceMonitorService = performanceMonitorService ?? throw new ArgumentNullException(nameof(performanceMonitorService));
            _processLoopService = processLoopService ?? throw new ArgumentNullException(nameof(processLoopService));
            _alarmManagementService = alarmManagementService ?? throw new ArgumentNullException(nameof(alarmManagementService));
            
            _mainCancellationTokenSource = new CancellationTokenSource();
            
            InitializeServices();
            InitializeProperties();
        }
        
        #endregion
        
        #region 基本属性
        
        [ObservableProperty]
        private string title = "Robot_IR400_2025-07-30-M1";
        
        [ObservableProperty]
        private bool isRunning;
        
        [ObservableProperty]
        private int waferTotalCount = TransferWaferConstants.DEFAULT_WAFER_TOTAL_COUNT;
        
        [ObservableProperty]
        private int loopCount = TransferWaferConstants.DEFAULT_LOOP_COUNT;
        
        [ObservableProperty]
        private int executedCount;
        
        [ObservableProperty]
        private bool hasStartedExecution;
        
        [ObservableProperty]
        private string currentStatus = "就绪";
        
        #endregion
        
        #region 搬运相关属性
        
        [ObservableProperty]
        private BContainer selectedFromChamber;
        
        [ObservableProperty]
        private BContainer selectedToChamber;
        
        [ObservableProperty]
        private int? selectedFromSlot;
        
        [ObservableProperty]
        private int? selectedToSlot;
        
        [ObservableProperty]
        private EnuArmFetchSide selectedByArmFetchSide;
        
        [ObservableProperty]
        private bool isRunCmd;
        
        [ObservableProperty]
        private string commandResult;
        
        #endregion
        
        #region PinSearch相关属性
        
        [ObservableProperty]
        private bool pinSearchBeforeTransfer;
        
        [ObservableProperty]
        private bool isPinSearchExecuting;
        
        [ObservableProperty]
        private string pinSearchStatus = "未执行";
        
        [ObservableProperty]
        private int smoothBasePinSearchValue;
        
        [ObservableProperty]
        private int noseBasePinSearchValue;
        
        [ObservableProperty]
        private string pinSearchLastExecuteTime = "";
        
        [ObservableProperty]
        private string smoothPinSearchDisplayText = "Smooth: ";
        
        [ObservableProperty]
        private string nosePinSearchDisplayText = "Nose: ";
        
        #endregion
        
        #region 命令实现
        
        /// <summary>
        /// 执行晶圆搬运命令 - 重构版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteTransfer))]
        private async Task TransferWaferAsync()
        {
            if (!ValidateTransferParameters())
                return;
            
            try
            {
                IsRunning = true;
                CurrentStatus = "执行搬运中...";
                
                var result = await _performanceMonitorService.MeasureAsync(async () =>
                {
                    return await _robotControlService.TransferWaferAsync(
                        SelectedFromChamber,
                        SelectedFromSlot.Value,
                        SelectedToChamber,
                        SelectedToSlot.Value,
                        SelectedByArmFetchSide,
                        _mainCancellationTokenSource.Token);
                }, 
                TransferWaferConstants.LogCategories.TRANSFER_OPERATION,
                TransferWaferConstants.TRANSFER_WARNING_THRESHOLD_MS);
                
                if (result.Result.Success)
                {
                    CommandResult = TransferWaferConstants.Messages.TRANSFER_SUCCESS;
                    CurrentStatus = "搬运完成";
                    UILogService.AddSuccessLog($"{TransferWaferConstants.Messages.TRANSFER_SUCCESS}: {result.Result.Message}");
                }
                else
                {
                    CommandResult = TransferWaferConstants.Messages.TRANSFER_FAILED;
                    CurrentStatus = "搬运失败";
                    UILogService.AddErrorLog($"{TransferWaferConstants.Messages.TRANSFER_FAILED}: {result.Result.Message}");
                }
                
                // 重置选择
                ResetTransferSelections();
            }
            catch (Exception ex)
            {
                HandleTransferError(ex);
            }
            finally
            {
                IsRunning = false;
            }
        }
        
        /// <summary>
        /// 执行流程循环命令 - 重构版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteLoop))]
        private async Task ExecuteProcessLoopAsync()
        {
            try
            {
                IsRunning = true;
                HasStartedExecution = true;
                ExecutedCount = 0;
                CurrentStatus = "循环执行中...";
                
                _loopCancellationTokenSource = new CancellationTokenSource();
                
                var loopConfig = new ProcessLoopConfiguration
                {
                    LoopCount = LoopCount,
                    WaferTotalCount = WaferTotalCount,
                    PinSearchEnabled = PinSearchBeforeTransfer,
                    CancellationToken = _loopCancellationTokenSource.Token
                };
                
                await _processLoopService.ExecuteProcessLoopAsync(loopConfig, OnLoopProgress);
                
                CurrentStatus = "循环完成";
                UILogService.AddSuccessLog(TransferWaferConstants.Messages.LOOP_STOPPED);
            }
            catch (OperationCanceledException)
            {
                CurrentStatus = "循环已取消";
                UILogService.AddWarningLog("流程循环被用户取消");
            }
            catch (Exception ex)
            {
                CurrentStatus = "循环异常";
                UILogService.AddErrorLog($"流程循环执行异常: {ex.Message}");
            }
            finally
            {
                IsRunning = false;
                _loopCancellationTokenSource?.Dispose();
                _loopCancellationTokenSource = null;
            }
        }
        
        /// <summary>
        /// 重置系统状态命令 - 重构版本
        /// </summary>
        [RelayCommand]
        private async Task ProcessResetAsync()
        {
            try
            {
                CurrentStatus = "重置中...";
                
                var result = await _performanceMonitorService.MeasureAsync(async () =>
                {
                    // 重置各个服务
                    await _robotControlService.ResetRobotAsync();
                    await _pinSearchService.ResetPinSearchDataAsync();
                    _alarmManagementService.ClearAllAlarms();
                    
                    // 重置UI状态
                    ResetUIState();
                    
                    return true;
                }, 
                "系统重置操作",
                TransferWaferConstants.PERFORMANCE_WARNING_THRESHOLD_MS);
                
                if (result.Result)
                {
                    CurrentStatus = "重置完成";
                    UILogService.AddSuccessLog(TransferWaferConstants.Messages.RESET_SUCCESS);
                }
            }
            catch (Exception ex)
            {
                CurrentStatus = "重置失败";
                UILogService.AddErrorLog($"系统重置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止流程命令 - 重构版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteStop))]
        private async Task ProcessStopAsync()
        {
            try
            {
                CurrentStatus = "停止中...";
                
                // 取消所有正在进行的操作
                _loopCancellationTokenSource?.Cancel();
                
                // 停止流程循环服务
                await _processLoopService.StopProcessLoopAsync();
                
                CurrentStatus = "已停止";
                UILogService.AddInfoLog("流程已停止");
            }
            catch (Exception ex)
            {
                CurrentStatus = "停止失败";
                UILogService.AddErrorLog($"停止流程失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// PinSearch开关命令 - 重构版本
        /// </summary>
        [RelayCommand]
        private void TogglePinSearchBeforeTransfer()
        {
            _pinSearchService.IsEnabled = PinSearchBeforeTransfer;
            
            var status = PinSearchBeforeTransfer ? "启用" : "禁用";
            UILogService.AddLog($"PinSearch选项已{status}");
            
            // 更新PinSearch状态显示
            UpdatePinSearchDisplay();
        }
        
        #endregion
        
        #region 命令可执行性检查
        
        private bool CanExecuteTransfer()
        {
            return !IsRunning && 
                   SelectedFromChamber != null && 
                   SelectedToChamber != null && 
                   SelectedFromSlot.HasValue && 
                   SelectedToSlot.HasValue;
        }
        
        private bool CanExecuteLoop()
        {
            return !IsRunning && LoopCount > 0;
        }
        
        private bool CanExecuteStop()
        {
            return IsRunning;
        }
        
        #endregion
        
        #region 私有方法
        
        private void InitializeServices()
        {
            // 订阅服务事件
            _robotControlService.TransferCompleted += OnTransferCompleted;
            _robotControlService.StatusChanged += OnRobotStatusChanged;
            
            _pinSearchService.PinSearchCompleted += OnPinSearchCompleted;
            _pinSearchService.StatusChanged += OnPinSearchStatusChanged;
            
            _performanceMonitorService.PerformanceWarning += OnPerformanceWarning;
            _performanceMonitorService.HighMemoryUsage += OnHighMemoryUsage;
            
            _alarmManagementService.AlarmAdded += OnAlarmAdded;
            _alarmManagementService.AlarmRemoved += OnAlarmRemoved;
        }
        
        private void InitializeProperties()
        {
            // 初始化PinSearch默认值
            SmoothBasePinSearchValue = Golbal.BasePinSearchResultVallue;
            NoseBasePinSearchValue = Golbal.BasePinSearchResultVallue;
            UpdatePinSearchDisplay();
        }
        
        private bool ValidateTransferParameters()
        {
            if (!SelectedFromSlot.HasValue || !SelectedToSlot.HasValue)
            {
                UILogService.AddWarningLog(TransferWaferConstants.Messages.SLOT_CANNOT_BE_EMPTY);
                return false;
            }
            
            if (SelectedFromChamber == null || SelectedToChamber == null)
            {
                UILogService.AddWarningLog("请选择源位置和目标位置");
                return false;
            }
            
            return true;
        }
        
        private void ResetTransferSelections()
        {
            SelectedFromSlot = null;
            SelectedToSlot = null;
            // 保持Chamber选择不变，方便连续操作
        }
        
        private void ResetUIState()
        {
            ExecutedCount = 0;
            HasStartedExecution = false;
            ResetTransferSelections();
            CommandResult = string.Empty;
        }
        
        private void HandleTransferError(Exception ex)
        {
            CommandResult = $"搬运异常: {ex.Message}";
            CurrentStatus = "搬运异常";
            UILogService.AddErrorLog($"晶圆搬运异常: {ex.Message}");
        }
        
        private void UpdatePinSearchDisplay()
        {
            var pinSearchStatus = _pinSearchService.GetCurrentStatus();
            
            SmoothPinSearchDisplayText = pinSearchStatus.IsSmoothValueNew ? "*Smooth: " : "Smooth: ";
            NosePinSearchDisplayText = pinSearchStatus.IsNoseValueNew ? "*Nose: " : "Nose: ";
            PinSearchStatus = pinSearchStatus.CurrentStatus;
            PinSearchLastExecuteTime = pinSearchStatus.LastExecuteTime?.ToString("HH:mm:ss") ?? "";
        }
        
        private void OnLoopProgress(ProcessLoopProgressEventArgs args)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                ExecutedCount = args.CompletedCount;
                CurrentStatus = args.CurrentOperation;
            });
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnTransferCompleted(object sender, TransferCompletedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                CommandResult = e.Result.Success ? "搬运成功" : "搬运失败";
            });
        }
        
        private void OnRobotStatusChanged(object sender, RobotStatusChangedEventArgs e)
        {
            // 更新Robot状态相关的UI
        }
        
        private void OnPinSearchCompleted(object sender, PinSearchCompletedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                UpdatePinSearchDisplay();
            });
        }
        
        private void OnPinSearchStatusChanged(object sender, PinSearchStatusChangedEventArgs e)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                UpdatePinSearchDisplay();
            });
        }
        
        private void OnPerformanceWarning(object sender, PerformanceWarningEventArgs e)
        {
            UILogService.AddWarningLog($"性能警告: {e.Message}");
        }
        
        private void OnHighMemoryUsage(object sender, MemoryUsageEventArgs e)
        {
            UILogService.AddWarningLog($"内存使用过高: {e.Message}");
        }
        
        private void OnAlarmAdded(object sender, AlarmEventArgs e)
        {
            // 处理报警添加
        }
        
        private void OnAlarmRemoved(object sender, AlarmEventArgs e)
        {
            // 处理报警移除
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            // 取消所有操作
            _mainCancellationTokenSource?.Cancel();
            _loopCancellationTokenSource?.Cancel();
            
            // 取消事件订阅
            if (_robotControlService != null)
            {
                _robotControlService.TransferCompleted -= OnTransferCompleted;
                _robotControlService.StatusChanged -= OnRobotStatusChanged;
            }
            
            if (_pinSearchService != null)
            {
                _pinSearchService.PinSearchCompleted -= OnPinSearchCompleted;
                _pinSearchService.StatusChanged -= OnPinSearchStatusChanged;
            }
            
            if (_performanceMonitorService != null)
            {
                _performanceMonitorService.PerformanceWarning -= OnPerformanceWarning;
                _performanceMonitorService.HighMemoryUsage -= OnHighMemoryUsage;
            }
            
            if (_alarmManagementService != null)
            {
                _alarmManagementService.AlarmAdded -= OnAlarmAdded;
                _alarmManagementService.AlarmRemoved -= OnAlarmRemoved;
            }
            
            // 释放资源
            _mainCancellationTokenSource?.Dispose();
            _loopCancellationTokenSource?.Dispose();
        }
        
        #endregion
    }
}
