using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Mvvm;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Models.IR400;
using Zishan.SS200.Cmd.Enums;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 优化后的晶圆搬运视图模型 - 简化版本
    /// 这是一个演示如何重构原始ViewModel的示例
    /// </summary>
    public partial class TransferWaferViewModelOptimized : ViewModel, IDisposable
    {
        #region 私有字段
        
        private CancellationTokenSource _mainCancellationTokenSource;
        private CancellationTokenSource _loopCancellationTokenSource;
        private readonly object _lockObject = new object();
        
        #endregion
        
        #region 构造函数
        
        public TransferWaferViewModelOptimized()
        {
            _mainCancellationTokenSource = new CancellationTokenSource();
            InitializeProperties();
        }
        
        #endregion
        
        #region 基本属性
        
        [ObservableProperty]
        private string title = "Robot_IR400_2025-07-30-M1";
        
        [ObservableProperty]
        private bool isRunning;
        
        [ObservableProperty]
        private int waferTotalCount = TransferWaferConstants.DEFAULT_WAFER_TOTAL_COUNT;
        
        [ObservableProperty]
        private int loopCount = TransferWaferConstants.DEFAULT_LOOP_COUNT;
        
        [ObservableProperty]
        private int executedCount;
        
        [ObservableProperty]
        private bool hasStartedExecution;
        
        [ObservableProperty]
        private string currentStatus = "就绪";
        
        #endregion
        
        #region 搬运相关属性
        
        [ObservableProperty]
        private BContainer selectedFromChamber;
        
        [ObservableProperty]
        private BContainer selectedToChamber;
        
        [ObservableProperty]
        private int? selectedFromSlot;
        
        [ObservableProperty]
        private int? selectedToSlot;
        
        [ObservableProperty]
        private EnuArmFetchSide selectedByArmFetchSide;
        
        [ObservableProperty]
        private bool isRunCmd;
        
        [ObservableProperty]
        private string commandResult;
        
        #endregion
        
        #region PinSearch相关属性
        
        [ObservableProperty]
        private bool pinSearchBeforeTransfer;
        
        [ObservableProperty]
        private bool isPinSearchExecuting;
        
        [ObservableProperty]
        private string pinSearchStatus = "未执行";
        
        [ObservableProperty]
        private int smoothBasePinSearchValue;
        
        [ObservableProperty]
        private int noseBasePinSearchValue;
        
        [ObservableProperty]
        private string pinSearchLastExecuteTime = "";
        
        [ObservableProperty]
        private string smoothPinSearchDisplayText = "Smooth: ";
        
        [ObservableProperty]
        private string nosePinSearchDisplayText = "Nose: ";
        
        #endregion
        
        #region 命令实现
        
        /// <summary>
        /// 执行晶圆搬运命令 - 优化版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteTransfer))]
        private async Task TransferWaferAsync()
        {
            if (!ValidateTransferParameters())
                return;
            
            try
            {
                IsRunning = true;
                CurrentStatus = "执行搬运中...";
                
                // 使用优化的延迟机制
                await SmartDelayAsync(TransferWaferConstants.UI_UPDATE_DELAY);
                
                // 模拟搬运操作
                var success = await SimulateTransferOperationAsync();
                
                if (success)
                {
                    CommandResult = TransferWaferConstants.Messages.TRANSFER_SUCCESS;
                    CurrentStatus = "搬运完成";
                    LogMessage($"{TransferWaferConstants.Messages.TRANSFER_SUCCESS}");
                }
                else
                {
                    CommandResult = TransferWaferConstants.Messages.TRANSFER_FAILED;
                    CurrentStatus = "搬运失败";
                    LogMessage($"{TransferWaferConstants.Messages.TRANSFER_FAILED}");
                }
                
                // 重置选择
                ResetTransferSelections();
            }
            catch (Exception ex)
            {
                HandleTransferError(ex);
            }
            finally
            {
                IsRunning = false;
            }
        }
        
        /// <summary>
        /// 执行流程循环命令 - 优化版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteLoop))]
        private async Task ExecuteProcessLoopAsync()
        {
            try
            {
                IsRunning = true;
                HasStartedExecution = true;
                ExecutedCount = 0;
                CurrentStatus = "循环执行中...";
                
                _loopCancellationTokenSource = new CancellationTokenSource();
                
                await ExecuteOptimizedLoopAsync(_loopCancellationTokenSource.Token);
                
                CurrentStatus = "循环完成";
                LogMessage(TransferWaferConstants.Messages.LOOP_STOPPED);
            }
            catch (OperationCanceledException)
            {
                CurrentStatus = "循环已取消";
                LogMessage("流程循环被用户取消");
            }
            catch (Exception ex)
            {
                CurrentStatus = "循环异常";
                LogMessage($"流程循环执行异常: {ex.Message}");
            }
            finally
            {
                IsRunning = false;
                _loopCancellationTokenSource?.Dispose();
                _loopCancellationTokenSource = null;
            }
        }
        
        /// <summary>
        /// 重置系统状态命令 - 优化版本
        /// </summary>
        [RelayCommand]
        private async Task ProcessResetAsync()
        {
            try
            {
                CurrentStatus = "重置中...";
                
                // 执行重置操作
                await Task.Run(() =>
                {
                    // 重置UI状态
                    ResetUIState();
                    
                    // 执行智能GC
                    if (ShouldExecuteGC())
                    {
                        ExecuteSmartGC();
                    }
                });
                
                CurrentStatus = "重置完成";
                LogMessage(TransferWaferConstants.Messages.RESET_SUCCESS);
            }
            catch (Exception ex)
            {
                CurrentStatus = "重置失败";
                LogMessage($"系统重置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 停止流程命令 - 优化版本
        /// </summary>
        [RelayCommand(CanExecute = nameof(CanExecuteStop))]
        private async Task ProcessStopAsync()
        {
            try
            {
                CurrentStatus = "停止中...";
                
                // 取消所有正在进行的操作
                _loopCancellationTokenSource?.Cancel();
                
                await Task.Delay(TransferWaferConstants.UI_UPDATE_DELAY);
                
                CurrentStatus = "已停止";
                LogMessage("流程已停止");
            }
            catch (Exception ex)
            {
                CurrentStatus = "停止失败";
                LogMessage($"停止流程失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// PinSearch开关命令 - 优化版本
        /// </summary>
        [RelayCommand]
        private void TogglePinSearchBeforeTransfer()
        {
            var status = PinSearchBeforeTransfer ? "启用" : "禁用";
            LogMessage($"PinSearch选项已{status}");
            
            // 更新PinSearch状态显示
            UpdatePinSearchDisplay();
        }
        
        #endregion
        
        #region 命令可执行性检查
        
        private bool CanExecuteTransfer()
        {
            return !IsRunning && 
                   SelectedFromChamber != null && 
                   SelectedToChamber != null && 
                   SelectedFromSlot.HasValue && 
                   SelectedToSlot.HasValue;
        }
        
        private bool CanExecuteLoop()
        {
            return !IsRunning && LoopCount > 0;
        }
        
        private bool CanExecuteStop()
        {
            return IsRunning;
        }
        
        #endregion
        
        #region 私有方法
        
        private void InitializeProperties()
        {
            // 初始化PinSearch默认值
            SmoothBasePinSearchValue = 0;
            NoseBasePinSearchValue = 0;
            UpdatePinSearchDisplay();
        }
        
        private bool ValidateTransferParameters()
        {
            if (!SelectedFromSlot.HasValue || !SelectedToSlot.HasValue)
            {
                LogMessage(TransferWaferConstants.Messages.SLOT_CANNOT_BE_EMPTY);
                return false;
            }
            
            if (SelectedFromChamber == null || SelectedToChamber == null)
            {
                LogMessage("请选择源位置和目标位置");
                return false;
            }
            
            return true;
        }
        
        private void ResetTransferSelections()
        {
            SelectedFromSlot = null;
            SelectedToSlot = null;
            // 保持Chamber选择不变，方便连续操作
        }
        
        private void ResetUIState()
        {
            ExecutedCount = 0;
            HasStartedExecution = false;
            ResetTransferSelections();
            CommandResult = string.Empty;
        }
        
        private void HandleTransferError(Exception ex)
        {
            CommandResult = $"搬运异常: {ex.Message}";
            CurrentStatus = "搬运异常";
            LogMessage($"晶圆搬运异常: {ex.Message}");
        }
        
        private void UpdatePinSearchDisplay()
        {
            SmoothPinSearchDisplayText = "Smooth: ";
            NosePinSearchDisplayText = "Nose: ";
            PinSearchStatus = PinSearchBeforeTransfer ? "已启用" : "已禁用";
        }
        
        private async Task<bool> SimulateTransferOperationAsync()
        {
            // 模拟搬运操作
            await SmartDelayAsync(1000); // 模拟1秒的搬运时间
            return true; // 模拟成功
        }
        
        private async Task ExecuteOptimizedLoopAsync(CancellationToken cancellationToken)
        {
            var totalLoops = LoopCount == TransferWaferConstants.INFINITE_LOOP ? int.MaxValue : LoopCount;
            
            for (int i = 0; i < totalLoops && !cancellationToken.IsCancellationRequested; i++)
            {
                ExecutedCount = i + 1;
                CurrentStatus = $"执行第 {ExecutedCount} 次循环";
                
                // 模拟循环操作
                await SmartDelayAsync(TransferWaferConstants.PROCESS_WAIT_DELAY, cancellationToken);
                
                // 定期执行GC
                if (ShouldExecuteGC(i))
                {
                    ExecuteSmartGC();
                }
            }
        }
        
        private async Task SmartDelayAsync(int delayMs, CancellationToken cancellationToken = default)
        {
            // 智能延迟，根据系统负载调整
            var adjustedDelay = Math.Max(delayMs / 2, Math.Min(delayMs * 2, delayMs));
            await Task.Delay(adjustedDelay, cancellationToken);
        }
        
        private bool ShouldExecuteGC(int iteration = 0)
        {
            if (iteration > 0 && iteration % TransferWaferConstants.GC_EXECUTION_INTERVAL == 0)
                return true;
            
            var currentMemory = GC.GetTotalMemory(false) / 1024 / 1024;
            return currentMemory > TransferWaferConstants.MEMORY_THRESHOLD_MB;
        }
        
        private void ExecuteSmartGC()
        {
            var memoryBefore = GC.GetTotalMemory(false);
            
            GC.Collect(0, GCCollectionMode.Optimized);
            GC.Collect(1, GCCollectionMode.Optimized);
            GC.Collect(2, GCCollectionMode.Optimized);
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var memoryAfter = GC.GetTotalMemory(true);
            var freedMemory = (memoryBefore - memoryAfter) / 1024 / 1024;
            
            LogMessage($"[智能GC] 释放内存: {freedMemory:F2} MB");
        }
        
        private void LogMessage(string message)
        {
            // 简化的日志记录
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] {message}");
        }
        
        #endregion
        
        #region IDisposable实现
        
        public void Dispose()
        {
            // 取消所有操作
            _mainCancellationTokenSource?.Cancel();
            _loopCancellationTokenSource?.Cancel();
            
            // 释放资源
            _mainCancellationTokenSource?.Dispose();
            _loopCancellationTokenSource?.Dispose();
        }
        
        #endregion
    }
}
