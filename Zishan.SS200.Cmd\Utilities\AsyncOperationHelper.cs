using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Constants;

namespace Zishan.SS200.Cmd.Utilities
{
    /// <summary>
    /// 异步操作辅助类 - 提供统一的异步操作模式
    /// </summary>
    public static class AsyncOperationHelper
    {
        #region 延迟操作优化

        /// <summary>
        /// 智能延迟 - 根据系统负载动态调整延迟时间
        /// </summary>
        /// <param name="baseDelayMs">基础延迟时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>延迟任务</returns>
        public static async Task SmartDelayAsync(int baseDelayMs, CancellationToken cancellationToken = default)
        {
            // 根据系统负载动态调整延迟时间
            var adjustedDelay = CalculateAdjustedDelay(baseDelayMs);

            try
            {
                await Task.Delay(adjustedDelay, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，正常情况
                throw;
            }
        }

        /// <summary>
        /// 批量延迟操作 - 避免频繁的小延迟
        /// </summary>
        /// <param name="operations">操作列表</param>
        /// <param name="batchSize">批次大小</param>
        /// <param name="batchDelayMs">批次间延迟</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>批量执行任务</returns>
        public static async Task ExecuteBatchOperationsAsync<T>(
            T[] operations,
            Func<T, CancellationToken, Task> operationHandler,
            int batchSize = 10,
            int batchDelayMs = TransferWaferConstants.UI_UPDATE_DELAY,
            CancellationToken cancellationToken = default)
        {
            for (int i = 0; i < operations.Length; i += batchSize)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batchEnd = Math.Min(i + batchSize, operations.Length);
                var batchTasks = new Task[batchEnd - i];

                // 并行执行批次内的操作
                for (int j = i; j < batchEnd; j++)
                {
                    var operation = operations[j];
                    batchTasks[j - i] = operationHandler(operation, cancellationToken);
                }

                await Task.WhenAll(batchTasks);

                // 批次间延迟
                if (i + batchSize < operations.Length)
                {
                    await SmartDelayAsync(batchDelayMs, cancellationToken);
                }
            }
        }

        #endregion

        #region 重试机制

        /// <summary>
        /// 带重试的异步操作执行
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="retryDelayMs">重试间隔（毫秒）</param>
        /// <param name="operationName">操作名称（用于日志）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteWithRetryAsync<T>(
            Func<CancellationToken, Task<T>> operation,
            int maxRetries = 3,
            int retryDelayMs = 1000,
            string operationName = "未知操作",
            CancellationToken cancellationToken = default)
        {
            Exception lastException = null;

            for (int attempt = 0; attempt <= maxRetries; attempt++)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    var result = await operation(cancellationToken);

                    if (attempt > 0)
                    {
                        // UILogService.AddSuccessLog($"{operationName} 在第{attempt + 1}次尝试后成功");
                    }

                    return result;
                }
                catch (OperationCanceledException)
                {
                    throw; // 不重试取消操作
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    if (attempt < maxRetries)
                    {
                        // UILogService.AddWarningLog($"{operationName} 第{attempt + 1}次尝试失败: {ex.Message}，{retryDelayMs}ms后重试");
                        await SmartDelayAsync(retryDelayMs, cancellationToken);
                        retryDelayMs = Math.Min(retryDelayMs * 2, 10000); // 指数退避，最大10秒
                    }
                    else
                    {
                        // UILogService.AddErrorLog($"{operationName} 在{maxRetries + 1}次尝试后仍然失败");
                    }
                }
            }

            throw new OperationFailedException($"{operationName} 执行失败", lastException);
        }

        #endregion

        #region 超时控制

        /// <summary>
        /// 带超时的异步操作执行
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>操作结果</returns>
        public static async Task<T> ExecuteWithTimeoutAsync<T>(
            Func<CancellationToken, Task<T>> operation,
            int timeoutMs,
            string operationName = "未知操作",
            CancellationToken cancellationToken = default)
        {
            using var timeoutCts = new CancellationTokenSource(timeoutMs);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            try
            {
                return await operation(combinedCts.Token);
            }
            catch (OperationCanceledException) when (timeoutCts.Token.IsCancellationRequested)
            {
                throw new TimeoutException($"{operationName} 操作超时（{timeoutMs}ms）");
            }
        }

        #endregion

        #region UI线程操作

        /// <summary>
        /// 安全的UI线程调用
        /// </summary>
        /// <param name="action">要在UI线程执行的操作</param>
        /// <param name="priority">调度优先级</param>
        /// <returns>调度任务</returns>
        public static async Task InvokeOnUIThreadAsync(Action action, System.Windows.Threading.DispatcherPriority priority = System.Windows.Threading.DispatcherPriority.Normal)
        {
            if (Application.Current?.Dispatcher == null)
            {
                // UILogService.AddWarningLog("UI Dispatcher 不可用，跳过UI更新");
                return;
            }

            if (Application.Current.Dispatcher.CheckAccess())
            {
                action();
            }
            else
            {
                await Application.Current.Dispatcher.InvokeAsync(action, priority);
            }
        }

        /// <summary>
        /// 安全的UI线程调用（带返回值）
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="func">要在UI线程执行的函数</param>
        /// <param name="priority">调度优先级</param>
        /// <returns>函数结果</returns>
        public static async Task<T> InvokeOnUIThreadAsync<T>(Func<T> func, System.Windows.Threading.DispatcherPriority priority = System.Windows.Threading.DispatcherPriority.Normal)
        {
            if (Application.Current?.Dispatcher == null)
            {
                // UILogService.AddWarningLog("UI Dispatcher 不可用，返回默认值");
                return default(T);
            }

            if (Application.Current.Dispatcher.CheckAccess())
            {
                return func();
            }
            else
            {
                return await Application.Current.Dispatcher.InvokeAsync(func, priority);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 计算调整后的延迟时间
        /// </summary>
        /// <param name="baseDelayMs">基础延迟时间</param>
        /// <returns>调整后的延迟时间</returns>
        private static int CalculateAdjustedDelay(int baseDelayMs)
        {
            // 根据系统负载调整延迟时间
            var cpuUsage = GetCPUUsage();
            var memoryUsage = GetMemoryUsagePercentage();

            // 系统负载高时增加延迟，负载低时减少延迟
            var loadFactor = Math.Max(cpuUsage, memoryUsage) / 100.0;
            var adjustmentFactor = 1.0 + (loadFactor - 0.5) * 0.5; // 负载50%时不调整，负载高时增加延迟

            var adjustedDelay = (int)(baseDelayMs * Math.Max(0.5, Math.Min(2.0, adjustmentFactor)));

            return adjustedDelay;
        }

        /// <summary>
        /// 获取CPU使用率（简化版本）
        /// </summary>
        /// <returns>CPU使用率百分比</returns>
        private static double GetCPUUsage()
        {
            // 简化实现，实际项目中可以使用PerformanceCounter
            return 50.0; // 默认返回50%
        }

        /// <summary>
        /// 获取内存使用率
        /// </summary>
        /// <returns>内存使用率百分比</returns>
        private static double GetMemoryUsagePercentage()
        {
            var totalMemory = GC.GetTotalMemory(false);
            var workingSet = Environment.WorkingSet;

            return Math.Min(100.0, (double)totalMemory / workingSet * 100);
        }

        #endregion
    }

    /// <summary>
    /// 操作失败异常
    /// </summary>
    public class OperationFailedException : Exception
    {
        public OperationFailedException(string message) : base(message) { }
        public OperationFailedException(string message, Exception innerException) : base(message, innerException) { }
    }
}
