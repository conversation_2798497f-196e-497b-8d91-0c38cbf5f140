using System;
using System.Threading;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;
using <PERSON>ishan.SS200.Cmd.Models;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// 机器人控制服务接口
    /// </summary>
    public interface IRobotControlService
    {
        #region 属性
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 左机械臂
        /// </summary>
        RobotArmNew LeftRobotArm { get; }
        
        /// <summary>
        /// 右机械臂
        /// </summary>
        RobotArmNew RightRobotArm { get; }
        
        #endregion
        
        #region 搬运操作
        
        /// <summary>
        /// 执行晶圆搬运操作
        /// </summary>
        /// <param name="fromChamber">源腔体</param>
        /// <param name="fromSlot">源槽位</param>
        /// <param name="toChamber">目标腔体</param>
        /// <param name="toSlot">目标槽位</param>
        /// <param name="armSide">机械臂端</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>搬运结果</returns>
        Task<TransferResult> TransferWaferAsync(
            BContainer fromChamber, 
            int fromSlot, 
            BContainer toChamber, 
            int toSlot, 
            EnuArmFetchSide armSide,
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行两步搬运（腔体到腔体）
        /// </summary>
        /// <param name="fromChamber">源腔体</param>
        /// <param name="fromSlot">源槽位</param>
        /// <param name="toChamber">目标腔体</param>
        /// <param name="toSlot">目标槽位</param>
        /// <param name="armSide">机械臂端</param>
        /// <param name="cassette">Cassette参数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>搬运结果</returns>
        Task<TransferResult> TransferWaferTwoStepAsync(
            BContainer fromChamber, 
            int fromSlot, 
            BContainer toChamber, 
            int toSlot, 
            EnuArmFetchSide armSide,
            Cassette cassette,
            CancellationToken cancellationToken = default);
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 重置机器人状态
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetRobotAsync();
        
        /// <summary>
        /// 检查机器人是否可以执行操作
        /// </summary>
        /// <returns>检查结果</returns>
        bool CanExecuteOperation();
        
        /// <summary>
        /// 获取机器人当前状态
        /// </summary>
        /// <returns>状态信息</returns>
        RobotStatus GetRobotStatus();
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 搬运操作完成事件
        /// </summary>
        event EventHandler<TransferCompletedEventArgs> TransferCompleted;
        
        /// <summary>
        /// 机器人状态变更事件
        /// </summary>
        event EventHandler<RobotStatusChangedEventArgs> StatusChanged;
        
        #endregion
    }
    
    /// <summary>
    /// 搬运结果
    /// </summary>
    public class TransferResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public TimeSpan Duration { get; set; }
        public Exception Exception { get; set; }
        
        public static TransferResult CreateSuccess(string message, TimeSpan duration)
        {
            return new TransferResult { Success = true, Message = message, Duration = duration };
        }
        
        public static TransferResult CreateFailure(string message, Exception exception = null)
        {
            return new TransferResult { Success = false, Message = message, Exception = exception };
        }
    }
    
    /// <summary>
    /// 搬运完成事件参数
    /// </summary>
    public class TransferCompletedEventArgs : EventArgs
    {
        public TransferResult Result { get; set; }
        public string FromChamber { get; set; }
        public string ToChamber { get; set; }
        public int FromSlot { get; set; }
        public int ToSlot { get; set; }
    }
    
    /// <summary>
    /// 机器人状态变更事件参数
    /// </summary>
    public class RobotStatusChangedEventArgs : EventArgs
    {
        public RobotStatus OldStatus { get; set; }
        public RobotStatus NewStatus { get; set; }
    }
    
    /// <summary>
    /// 机器人状态
    /// </summary>
    public class RobotStatus
    {
        public bool IsConnected { get; set; }
        public bool IsRunning { get; set; }
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; }
        public bool LeftArmHasWafer { get; set; }
        public bool RightArmHasWafer { get; set; }
        public DateTime LastUpdateTime { get; set; }
    }
}
