using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// 报警管理服务接口
    /// </summary>
    public interface IAlarmManagementService
    {
        #region 属性
        
        /// <summary>
        /// 当前活动报警数量
        /// </summary>
        int ActiveAlarmCount { get; }
        
        /// <summary>
        /// 是否有严重报警
        /// </summary>
        bool HasCriticalAlarms { get; }
        
        #endregion
        
        #region 报警管理
        
        /// <summary>
        /// 添加报警
        /// </summary>
        /// <param name="alarm">报警信息</param>
        void AddAlarm(AlarmInfo alarm);
        
        /// <summary>
        /// 移除报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <returns>是否成功移除</returns>
        bool RemoveAlarm(string alarmId);
        
        /// <summary>
        /// 清除所有报警
        /// </summary>
        void ClearAllAlarms();
        
        /// <summary>
        /// 获取所有活动报警
        /// </summary>
        /// <returns>报警列表</returns>
        IEnumerable<AlarmInfo> GetActiveAlarms();
        
        /// <summary>
        /// 获取指定级别的报警
        /// </summary>
        /// <param name="level">报警级别</param>
        /// <returns>报警列表</returns>
        IEnumerable<AlarmInfo> GetAlarmsByLevel(AlarmLevel level);
        
        #endregion
        
        #region 报警确认
        
        /// <summary>
        /// 确认报警
        /// </summary>
        /// <param name="alarmId">报警ID</param>
        /// <param name="acknowledgedBy">确认人</param>
        /// <returns>是否成功确认</returns>
        bool AcknowledgeAlarm(string alarmId, string acknowledgedBy = "System");
        
        /// <summary>
        /// 确认所有报警
        /// </summary>
        /// <param name="acknowledgedBy">确认人</param>
        /// <returns>确认的报警数量</returns>
        int AcknowledgeAllAlarms(string acknowledgedBy = "System");
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 报警添加事件
        /// </summary>
        event EventHandler<AlarmEventArgs> AlarmAdded;
        
        /// <summary>
        /// 报警移除事件
        /// </summary>
        event EventHandler<AlarmEventArgs> AlarmRemoved;
        
        /// <summary>
        /// 报警确认事件
        /// </summary>
        event EventHandler<AlarmEventArgs> AlarmAcknowledged;
        
        #endregion
    }
    
    /// <summary>
    /// 报警信息
    /// </summary>
    public class AlarmInfo
    {
        /// <summary>
        /// 报警ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 报警代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 报警消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 报警级别
        /// </summary>
        public AlarmLevel Level { get; set; }
        
        /// <summary>
        /// 报警来源
        /// </summary>
        public string Source { get; set; }
        
        /// <summary>
        /// 发生时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 是否已确认
        /// </summary>
        public bool IsAcknowledged { get; set; }
        
        /// <summary>
        /// 确认时间
        /// </summary>
        public DateTime? AcknowledgedTime { get; set; }
        
        /// <summary>
        /// 确认人
        /// </summary>
        public string AcknowledgedBy { get; set; }
        
        /// <summary>
        /// 附加数据
        /// </summary>
        public Dictionary<string, object> AdditionalData { get; set; } = new Dictionary<string, object>();
    }
    
    /// <summary>
    /// 报警级别
    /// </summary>
    public enum AlarmLevel
    {
        /// <summary>
        /// 信息
        /// </summary>
        Info = 0,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,
        
        /// <summary>
        /// 严重
        /// </summary>
        Critical = 3
    }
    
    /// <summary>
    /// 报警事件参数
    /// </summary>
    public class AlarmEventArgs : EventArgs
    {
        public AlarmInfo Alarm { get; set; }
        
        public AlarmEventArgs(AlarmInfo alarm)
        {
            Alarm = alarm;
        }
    }
}
