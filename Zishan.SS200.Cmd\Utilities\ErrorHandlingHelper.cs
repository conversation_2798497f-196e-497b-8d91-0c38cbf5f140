using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using System.Windows;
using Wu.Wpf.Common;
using Zishan.SS200.Cmd.Constants;

namespace Zishan.SS200.Cmd.Utilities
{
    /// <summary>
    /// 统一错误处理辅助类
    /// </summary>
    public static class ErrorHandlingHelper
    {
        #region 私有字段

        private static readonly ConcurrentDictionary<string, DateTime> _lastErrorTimes = new ConcurrentDictionary<string, DateTime>();
        private static readonly TimeSpan _errorThrottleInterval = TimeSpan.FromSeconds(5); // 5秒内相同错误只记录一次

        #endregion

        #region 异常处理

        /// <summary>
        /// 处理并记录异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <param name="logCategory">日志分类</param>
        /// <returns>处理结果</returns>
        public static ErrorHandlingResult HandleException(
            Exception exception,
            string operationName,
            bool showUserMessage = true,
            string logCategory = TransferWaferConstants.LogCategories.ERROR_HANDLING)
        {
            if (exception == null)
                return ErrorHandlingResult.CreateSuccess();

            var errorKey = $"{operationName}:{exception.GetType().Name}:{exception.Message}";
            var now = DateTime.Now;

            // 检查是否需要节流（避免相同错误频繁记录）
            if (_lastErrorTimes.TryGetValue(errorKey, out var lastTime) &&
                now - lastTime < _errorThrottleInterval)
            {
                return ErrorHandlingResult.CreateThrottled(exception);
            }

            _lastErrorTimes.AddOrUpdate(errorKey, now, (key, oldValue) => now);

            var errorInfo = AnalyzeException(exception, operationName);

            // 记录日志
            LogError(errorInfo, logCategory);

            // 显示用户消息
            if (showUserMessage)
            {
                ShowUserErrorMessage(errorInfo);
            }

            return ErrorHandlingResult.CreateHandled(exception, errorInfo);
        }

        /// <summary>
        /// 异步处理异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <param name="logCategory">日志分类</param>
        /// <returns>处理结果</returns>
        public static async Task<ErrorHandlingResult> HandleExceptionAsync(
            Exception exception,
            string operationName,
            bool showUserMessage = true,
            string logCategory = TransferWaferConstants.LogCategories.ERROR_HANDLING)
        {
            return await Task.Run(() => HandleException(exception, operationName, showUserMessage, logCategory));
        }

        /// <summary>
        /// 安全执行操作（捕获并处理异常）
        /// </summary>
        /// <param name="operation">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <returns>执行结果</returns>
        public static SafeExecutionResult SafeExecute(
            Action operation,
            string operationName,
            bool showUserMessage = true)
        {
            try
            {
                operation();
                return SafeExecutionResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                var errorResult = HandleException(ex, operationName, showUserMessage);
                return SafeExecutionResult.CreateFailure(errorResult);
            }
        }

        /// <summary>
        /// 安全执行异步操作（捕获并处理异常）
        /// </summary>
        /// <param name="operation">要执行的异步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <returns>执行结果</returns>
        public static async Task<SafeExecutionResult> SafeExecuteAsync(
            Func<Task> operation,
            string operationName,
            bool showUserMessage = true)
        {
            try
            {
                await operation();
                return SafeExecutionResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                var errorResult = await HandleExceptionAsync(ex, operationName, showUserMessage);
                return SafeExecutionResult.CreateFailure(errorResult);
            }
        }

        /// <summary>
        /// 安全执行带返回值的操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="defaultValue">默认返回值</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <returns>执行结果</returns>
        public static SafeExecutionResult<T> SafeExecute<T>(
            Func<T> operation,
            string operationName,
            T defaultValue = default(T),
            bool showUserMessage = true)
        {
            try
            {
                var result = operation();
                return SafeExecutionResult<T>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                var errorResult = HandleException(ex, operationName, showUserMessage);
                return SafeExecutionResult<T>.CreateFailure(defaultValue, errorResult);
            }
        }

        /// <summary>
        /// 安全执行带返回值的异步操作
        /// </summary>
        /// <typeparam name="T">返回类型</typeparam>
        /// <param name="operation">要执行的异步操作</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="defaultValue">默认返回值</param>
        /// <param name="showUserMessage">是否显示用户消息</param>
        /// <returns>执行结果</returns>
        public static async Task<SafeExecutionResult<T>> SafeExecuteAsync<T>(
            Func<Task<T>> operation,
            string operationName,
            T defaultValue = default(T),
            bool showUserMessage = true)
        {
            try
            {
                var result = await operation();
                return SafeExecutionResult<T>.CreateSuccess(result);
            }
            catch (Exception ex)
            {
                var errorResult = await HandleExceptionAsync(ex, operationName, showUserMessage);
                return SafeExecutionResult<T>.CreateFailure(defaultValue, errorResult);
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 分析异常信息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="operationName">操作名称</param>
        /// <returns>错误信息</returns>
        private static ErrorInfo AnalyzeException(Exception exception, string operationName)
        {
            var errorInfo = new ErrorInfo
            {
                OperationName = operationName,
                ExceptionType = exception.GetType().Name,
                Message = exception.Message,
                StackTrace = exception.StackTrace,
                Timestamp = DateTime.Now,
                Severity = DetermineErrorSeverity(exception)
            };

            // 分析内部异常
            if (exception.InnerException != null)
            {
                errorInfo.InnerExceptionMessage = exception.InnerException.Message;
                errorInfo.InnerExceptionType = exception.InnerException.GetType().Name;
            }

            // 生成用户友好的错误消息
            errorInfo.UserFriendlyMessage = GenerateUserFriendlyMessage(exception, operationName);

            // 生成建议的解决方案
            errorInfo.SuggestedSolution = GenerateSuggestedSolution(exception);

            return errorInfo;
        }

        /// <summary>
        /// 确定错误严重程度
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>错误严重程度</returns>
        private static ErrorSeverity DetermineErrorSeverity(Exception exception)
        {
            return exception switch
            {
                OperationCanceledException => ErrorSeverity.Info,
                ArgumentException => ErrorSeverity.Warning,
                InvalidOperationException => ErrorSeverity.Warning,
                TimeoutException => ErrorSeverity.Warning,
                UnauthorizedAccessException => ErrorSeverity.Error,
                OutOfMemoryException => ErrorSeverity.Critical,
                StackOverflowException => ErrorSeverity.Critical,
                _ => ErrorSeverity.Error
            };
        }

        /// <summary>
        /// 生成用户友好的错误消息
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="operationName">操作名称</param>
        /// <returns>用户友好的错误消息</returns>
        private static string GenerateUserFriendlyMessage(Exception exception, string operationName)
        {
            return exception switch
            {
                OperationCanceledException => $"{operationName}操作已被取消",
                TimeoutException => $"{operationName}操作超时，请检查设备连接状态",
                UnauthorizedAccessException => $"{operationName}操作权限不足，请检查用户权限",
                ArgumentException => $"{operationName}操作参数错误，请检查输入参数",
                InvalidOperationException => $"{operationName}操作在当前状态下无法执行",
                OutOfMemoryException => "系统内存不足，请关闭其他应用程序后重试",
                _ => $"{operationName}操作失败：{exception.Message}"
            };
        }

        /// <summary>
        /// 生成建议的解决方案
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <returns>建议的解决方案</returns>
        private static string GenerateSuggestedSolution(Exception exception)
        {
            return exception switch
            {
                OperationCanceledException => "操作已取消，如需继续请重新执行",
                TimeoutException => "检查设备连接状态，确保网络通畅",
                UnauthorizedAccessException => "联系管理员获取相应权限",
                ArgumentException => "检查输入参数是否正确",
                InvalidOperationException => "确保系统处于正确状态后重试",
                OutOfMemoryException => "关闭不必要的应用程序，释放内存后重试",
                _ => "请查看详细错误信息，如问题持续存在请联系技术支持"
            };
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="errorInfo">错误信息</param>
        /// <param name="logCategory">日志分类</param>
        private static void LogError(ErrorInfo errorInfo, string logCategory)
        {
            var logMessage = $"[{logCategory}] {errorInfo.OperationName} - {errorInfo.ExceptionType}: {errorInfo.Message}";

            switch (errorInfo.Severity)
            {
                case ErrorSeverity.Info:
                    // UILogService.AddInfoLog(logMessage);
                    break;
                case ErrorSeverity.Warning:
                    // UILogService.AddWarningLog(logMessage);
                    break;
                case ErrorSeverity.Error:
                    // UILogService.AddErrorLog(logMessage);
                    // AppLog.Error(logMessage, new Exception(errorInfo.Message));
                    break;
                case ErrorSeverity.Critical:
                    // UILogService.AddErrorLog($"[严重错误] {logMessage}");
                    // AppLog.Fatal(logMessage, new Exception(errorInfo.Message));
                    break;
            }
        }

        /// <summary>
        /// 显示用户错误消息
        /// </summary>
        /// <param name="errorInfo">错误信息</param>
        private static void ShowUserErrorMessage(ErrorInfo errorInfo)
        {
            if (errorInfo.Severity == ErrorSeverity.Info)
                return; // 信息级别不显示消息框

            var messageBoxImage = errorInfo.Severity switch
            {
                ErrorSeverity.Warning => MessageBoxImage.Warning,
                ErrorSeverity.Error => MessageBoxImage.Error,
                ErrorSeverity.Critical => MessageBoxImage.Stop,
                _ => MessageBoxImage.Information
            };

            var title = errorInfo.Severity switch
            {
                ErrorSeverity.Warning => "警告",
                ErrorSeverity.Error => "错误",
                ErrorSeverity.Critical => "严重错误",
                _ => "信息"
            };

            var message = $"{errorInfo.UserFriendlyMessage}\n\n建议解决方案：\n{errorInfo.SuggestedSolution}";

            // 在UI线程显示消息框
            Application.Current?.Dispatcher?.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, messageBoxImage);
            });
        }

        #endregion
    }

    #region 结果类定义

    /// <summary>
    /// 错误处理结果
    /// </summary>
    public class ErrorHandlingResult
    {
        public bool IsHandled { get; set; }
        public bool IsThrottled { get; set; }
        public Exception Exception { get; set; }
        public ErrorInfo ErrorInfo { get; set; }

        public static ErrorHandlingResult CreateSuccess() => new ErrorHandlingResult { IsHandled = true };
        public static ErrorHandlingResult CreateHandled(Exception exception, ErrorInfo errorInfo) =>
            new ErrorHandlingResult { IsHandled = true, Exception = exception, ErrorInfo = errorInfo };
        public static ErrorHandlingResult CreateThrottled(Exception exception) =>
            new ErrorHandlingResult { IsHandled = true, IsThrottled = true, Exception = exception };
    }

    /// <summary>
    /// 安全执行结果
    /// </summary>
    public class SafeExecutionResult
    {
        public bool Success { get; set; }
        public ErrorHandlingResult ErrorResult { get; set; }

        public static SafeExecutionResult CreateSuccess() => new SafeExecutionResult { Success = true };
        public static SafeExecutionResult CreateFailure(ErrorHandlingResult errorResult) =>
            new SafeExecutionResult { Success = false, ErrorResult = errorResult };
    }

    /// <summary>
    /// 带返回值的安全执行结果
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    public class SafeExecutionResult<T> : SafeExecutionResult
    {
        public T Result { get; set; }

        public static SafeExecutionResult<T> CreateSuccess(T result) =>
            new SafeExecutionResult<T> { Success = true, Result = result };
        public static SafeExecutionResult<T> CreateFailure(T defaultValue, ErrorHandlingResult errorResult) =>
            new SafeExecutionResult<T> { Success = false, Result = defaultValue, ErrorResult = errorResult };
    }

    /// <summary>
    /// 错误信息
    /// </summary>
    public class ErrorInfo
    {
        public string OperationName { get; set; }
        public string ExceptionType { get; set; }
        public string Message { get; set; }
        public string StackTrace { get; set; }
        public string InnerExceptionType { get; set; }
        public string InnerExceptionMessage { get; set; }
        public DateTime Timestamp { get; set; }
        public ErrorSeverity Severity { get; set; }
        public string UserFriendlyMessage { get; set; }
        public string SuggestedSolution { get; set; }
    }

    /// <summary>
    /// 错误严重程度
    /// </summary>
    public enum ErrorSeverity
    {
        Info,
        Warning,
        Error,
        Critical
    }

    #endregion
}
