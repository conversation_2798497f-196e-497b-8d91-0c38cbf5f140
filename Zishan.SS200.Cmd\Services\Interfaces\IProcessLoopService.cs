using System;
using System.Threading;
using System.Threading.Tasks;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// 流程循环服务接口
    /// </summary>
    public interface IProcessLoopService
    {
        #region 属性
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 当前循环次数
        /// </summary>
        int CurrentLoopCount { get; }
        
        /// <summary>
        /// 总循环次数
        /// </summary>
        int TotalLoopCount { get; }
        
        #endregion
        
        #region 方法
        
        /// <summary>
        /// 执行流程循环
        /// </summary>
        /// <param name="configuration">循环配置</param>
        /// <param name="progressCallback">进度回调</param>
        /// <returns>执行结果</returns>
        Task<bool> ExecuteProcessLoopAsync(ProcessLoopConfiguration configuration, Action<ProcessLoopProgressEventArgs> progressCallback = null);
        
        /// <summary>
        /// 停止流程循环
        /// </summary>
        /// <returns>停止结果</returns>
        Task<bool> StopProcessLoopAsync();
        
        /// <summary>
        /// 暂停流程循环
        /// </summary>
        /// <returns>暂停结果</returns>
        Task<bool> PauseProcessLoopAsync();
        
        /// <summary>
        /// 继续流程循环
        /// </summary>
        /// <returns>继续结果</returns>
        Task<bool> ResumeProcessLoopAsync();
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// 循环开始事件
        /// </summary>
        event EventHandler<ProcessLoopEventArgs> LoopStarted;
        
        /// <summary>
        /// 循环完成事件
        /// </summary>
        event EventHandler<ProcessLoopEventArgs> LoopCompleted;
        
        /// <summary>
        /// 循环进度事件
        /// </summary>
        event EventHandler<ProcessLoopProgressEventArgs> LoopProgress;
        
        #endregion
    }
    
    /// <summary>
    /// 流程循环配置
    /// </summary>
    public class ProcessLoopConfiguration
    {
        /// <summary>
        /// 循环次数
        /// </summary>
        public int LoopCount { get; set; }
        
        /// <summary>
        /// Wafer总数
        /// </summary>
        public int WaferTotalCount { get; set; }
        
        /// <summary>
        /// 是否启用PinSearch
        /// </summary>
        public bool PinSearchEnabled { get; set; }
        
        /// <summary>
        /// 取消令牌
        /// </summary>
        public CancellationToken CancellationToken { get; set; }
        
        /// <summary>
        /// 配方名称
        /// </summary>
        public string RecipeName { get; set; }
        
        /// <summary>
        /// 延迟配置
        /// </summary>
        public int DelayBetweenOperations { get; set; } = 100;
    }
    
    /// <summary>
    /// 流程循环事件参数
    /// </summary>
    public class ProcessLoopEventArgs : EventArgs
    {
        public int LoopCount { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
    }
    
    /// <summary>
    /// 流程循环进度事件参数
    /// </summary>
    public class ProcessLoopProgressEventArgs : EventArgs
    {
        /// <summary>
        /// 已完成次数
        /// </summary>
        public int CompletedCount { get; set; }
        
        /// <summary>
        /// 总次数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 当前操作
        /// </summary>
        public string CurrentOperation { get; set; }
        
        /// <summary>
        /// 进度百分比
        /// </summary>
        public double ProgressPercentage { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
