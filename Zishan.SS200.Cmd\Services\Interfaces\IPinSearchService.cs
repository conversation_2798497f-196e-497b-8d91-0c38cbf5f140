using System;
using System.Threading;
using System.Threading.Tasks;
using Zishan.SS200.Cmd.Enums.SS200.SubsystemConfigure.Robot;

namespace Zishan.SS200.Cmd.Services.Interfaces
{
    /// <summary>
    /// PinSearch服务接口
    /// </summary>
    public interface IPinSearchService
    {
        #region 属性
        
        /// <summary>
        /// 是否启用PinSearch
        /// </summary>
        bool IsEnabled { get; set; }
        
        /// <summary>
        /// 是否正在执行
        /// </summary>
        bool IsExecuting { get; }
        
        /// <summary>
        /// Smooth端基准值
        /// </summary>
        int SmoothBasePinSearchValue { get; }
        
        /// <summary>
        /// Nose端基准值
        /// </summary>
        int NoseBasePinSearchValue { get; }
        
        /// <summary>
        /// 执行状态
        /// </summary>
        string Status { get; }
        
        /// <summary>
        /// 最后执行时间
        /// </summary>
        DateTime? LastExecuteTime { get; }
        
        #endregion
        
        #region 执行方法
        
        /// <summary>
        /// 执行完整的PinSearch操作（包含Smooth和Nose端）
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<PinSearchResult> ExecuteFullPinSearchAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 执行单端PinSearch操作
        /// </summary>
        /// <param name="endType">端类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        Task<PinSearchResult> ExecuteSinglePinSearchAsync(EnuRobotEndType endType, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 重置PinSearch数据
        /// </summary>
        /// <returns>重置结果</returns>
        Task<bool> ResetPinSearchDataAsync();
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 获取当前PinSearch状态
        /// </summary>
        /// <returns>状态信息</returns>
        PinSearchStatus GetCurrentStatus();
        
        /// <summary>
        /// 验证PinSearch值是否有效
        /// </summary>
        /// <param name="smoothValue">Smooth值</param>
        /// <param name="noseValue">Nose值</param>
        /// <returns>验证结果</returns>
        bool ValidatePinSearchValues(int smoothValue, int noseValue);
        
        #endregion
        
        #region 事件
        
        /// <summary>
        /// PinSearch完成事件
        /// </summary>
        event EventHandler<PinSearchCompletedEventArgs> PinSearchCompleted;
        
        /// <summary>
        /// 状态变更事件
        /// </summary>
        event EventHandler<PinSearchStatusChangedEventArgs> StatusChanged;
        
        #endregion
    }
    
    /// <summary>
    /// PinSearch执行结果
    /// </summary>
    public class PinSearchResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int SmoothValue { get; set; }
        public int NoseValue { get; set; }
        public TimeSpan Duration { get; set; }
        public Exception Exception { get; set; }
        public bool IsNewValue { get; set; }
        
        public static PinSearchResult CreateSuccess(string message, int smoothValue, int noseValue, TimeSpan duration, bool isNewValue = true)
        {
            return new PinSearchResult 
            { 
                Success = true, 
                Message = message, 
                SmoothValue = smoothValue, 
                NoseValue = noseValue, 
                Duration = duration,
                IsNewValue = isNewValue
            };
        }
        
        public static PinSearchResult CreateFailure(string message, Exception exception = null)
        {
            return new PinSearchResult { Success = false, Message = message, Exception = exception };
        }
    }
    
    /// <summary>
    /// PinSearch状态
    /// </summary>
    public class PinSearchStatus
    {
        public bool IsEnabled { get; set; }
        public bool IsExecuting { get; set; }
        public string CurrentStatus { get; set; }
        public int SmoothValue { get; set; }
        public int NoseValue { get; set; }
        public bool IsSmoothValueNew { get; set; }
        public bool IsNoseValueNew { get; set; }
        public DateTime? LastExecuteTime { get; set; }
        public bool IsConnected { get; set; }
    }
    
    /// <summary>
    /// PinSearch完成事件参数
    /// </summary>
    public class PinSearchCompletedEventArgs : EventArgs
    {
        public PinSearchResult Result { get; set; }
        public EnuRobotEndType EndType { get; set; }
    }
    
    /// <summary>
    /// PinSearch状态变更事件参数
    /// </summary>
    public class PinSearchStatusChangedEventArgs : EventArgs
    {
        public PinSearchStatus OldStatus { get; set; }
        public PinSearchStatus NewStatus { get; set; }
    }
}
